#include "ui/vulkan_shader.h"
#include "ui/vulkan_context.h"
#include <QFile>
#include <QFileInfo>

namespace Vizion3D::UI {

VulkanShader::VulkanShader() = default;

VulkanShader::~VulkanShader() {
    cleanup();
}

VulkanShader::VulkanShader(VulkanShader&& other) noexcept
    : m_device(other.m_device)
    , m_pipeline(other.m_pipeline)
    , m_pipelineLayout(other.m_pipelineLayout)
    , m_vertexShader(other.m_vertexShader)
    , m_fragmentShader(other.m_fragmentShader)
    , m_context(other.m_context) {
    
    // Reset other object
    other.m_device = VK_NULL_HANDLE;
    other.m_pipeline = VK_NULL_HANDLE;
    other.m_pipelineLayout = VK_NULL_HANDLE;
    other.m_vertexShader = VK_NULL_HANDLE;
    other.m_fragmentShader = VK_NULL_HANDLE;
    other.m_context = nullptr;
}

VulkanShader& VulkanShader::operator=(VulkanShader&& other) noexcept {
    if (this != &other) {
        cleanup();
        
        m_device = other.m_device;
        m_pipeline = other.m_pipeline;
        m_pipelineLayout = other.m_pipelineLayout;
        m_vertexShader = other.m_vertexShader;
        m_fragmentShader = other.m_fragmentShader;
        m_context = other.m_context;
        
        // Reset other object
        other.m_device = VK_NULL_HANDLE;
        other.m_pipeline = VK_NULL_HANDLE;
        other.m_pipelineLayout = VK_NULL_HANDLE;
        other.m_vertexShader = VK_NULL_HANDLE;
        other.m_fragmentShader = VK_NULL_HANDLE;
        other.m_context = nullptr;
    }
    return *this;
}

Vizion3D::Utils::Result<void> VulkanShader::createGraphicsPipeline(const VulkanContext* context,
                                                                   const PipelineCreateInfo& createInfo) {
    VIZION3D_PROFILE_FUNCTION();
    
    if (!context) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::InvalidArgument,
            "VulkanContext cannot be null"
        );
    }
    
    if (createInfo.renderPass == VK_NULL_HANDLE) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::InvalidArgument,
            "RenderPass cannot be null"
        );
    }
    
    // Clean up any existing resources
    cleanup();
    
    m_context = context;
    m_device = context->device();
    
    // Create vertex shader module from SPIR-V bytecode
    if (createInfo.vertexShaderSpirv.empty()) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanShaderModuleCreationFailed,
            "VulkanShader: Vertex shader SPIR-V bytecode is empty"
        );
    }

    auto vertexModuleResult = createShaderModule(context, createInfo.vertexShaderSpirv);
    if (vertexModuleResult.isError()) {
        return Vizion3D::Utils::Result<void>::error(vertexModuleResult.error());
    }
    m_vertexShader = vertexModuleResult.value();

    // Create fragment shader module from SPIR-V bytecode
    if (createInfo.fragmentShaderSpirv.empty()) {
        cleanup();
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanShaderModuleCreationFailed,
            "VulkanShader: Fragment shader SPIR-V bytecode is empty"
        );
    }

    auto fragmentModuleResult = createShaderModule(context, createInfo.fragmentShaderSpirv);
    if (fragmentModuleResult.isError()) {
        cleanup();
        return Vizion3D::Utils::Result<void>::error(fragmentModuleResult.error());
    }
    m_fragmentShader = fragmentModuleResult.value();
    
    // Create pipeline layout
    auto layoutResult = createPipelineLayout(createInfo.descriptorSetLayout);
    if (layoutResult.isError()) {
        cleanup();
        return Vizion3D::Utils::Result<void>::error(layoutResult.error());
    }
    
    // Create graphics pipeline
    auto pipelineResult = createPipeline(createInfo);
    if (pipelineResult.isError()) {
        cleanup();
        return Vizion3D::Utils::Result<void>::error(pipelineResult.error());
    }
    
    VLOG_INFO("Vulkan", QString("Created graphics pipeline with %1 vertex shader bytes and %2 fragment shader bytes")
              .arg(createInfo.vertexShaderSpirv.size() * sizeof(uint32_t))
              .arg(createInfo.fragmentShaderSpirv.size() * sizeof(uint32_t)));
    
    return Vizion3D::Utils::Result<void>::success();
}

Vizion3D::Utils::Result<std::vector<uint32_t>> VulkanShader::loadShaderFromFile(const QString& filePath) {
    VIZION3D_PROFILE_FUNCTION();
    
    QFile file(filePath);
    if (!file.exists()) {
        return Vizion3D::Utils::Result<std::vector<uint32_t>>::error(
            Vizion3D::Utils::ErrorCode::FileNotFound,
            QString("Shader file not found: %1").arg(filePath)
        );
    }
    
    if (!file.open(QIODevice::ReadOnly)) {
        return Vizion3D::Utils::Result<std::vector<uint32_t>>::error(
            Vizion3D::Utils::ErrorCode::FileReadError,
            QString("Failed to open shader file: %1").arg(filePath)
        );
    }
    
    QByteArray data = file.readAll();
    if (data.isEmpty()) {
        return Vizion3D::Utils::Result<std::vector<uint32_t>>::error(
            Vizion3D::Utils::ErrorCode::FileReadError,
            QString("Shader file is empty: %1").arg(filePath)
        );
    }
    
    // SPIR-V files should be aligned to 4 bytes
    if (data.size() % 4 != 0) {
        return Vizion3D::Utils::Result<std::vector<uint32_t>>::error(
            Vizion3D::Utils::ErrorCode::FileFormatError,
            QString("Invalid SPIR-V file format (not aligned to 4 bytes): %1").arg(filePath)
        );
    }
    
    std::vector<uint32_t> spirvCode(data.size() / 4);
    std::memcpy(spirvCode.data(), data.constData(), data.size());
    
    VLOG_DEBUG("Vulkan", QString("Loaded shader: %1 (%2 bytes)").arg(filePath).arg(data.size()));
    return Vizion3D::Utils::Result<std::vector<uint32_t>>::success(std::move(spirvCode));
}

Vizion3D::Utils::Result<VkShaderModule> VulkanShader::createShaderModule(const VulkanContext* context,
                                                                         const std::vector<uint32_t>& spirvCode) {
    if (!context) {
        return Vizion3D::Utils::Result<VkShaderModule>::error(
            Vizion3D::Utils::ErrorCode::InvalidArgument,
            "VulkanContext cannot be null"
        );
    }
    
    if (spirvCode.empty()) {
        return Vizion3D::Utils::Result<VkShaderModule>::error(
            Vizion3D::Utils::ErrorCode::InvalidArgument,
            "SPIR-V code cannot be empty"
        );
    }
    
    VkShaderModuleCreateInfo createInfo{};
    createInfo.sType = VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO;
    createInfo.codeSize = spirvCode.size() * sizeof(uint32_t);
    createInfo.pCode = spirvCode.data();
    
    VkShaderModule shaderModule;
    VkResult result = vkCreateShaderModule(context->device(), &createInfo, nullptr, &shaderModule);
    
    if (result != VK_SUCCESS) {
        return Vizion3D::Utils::Result<VkShaderModule>::error(
            Vizion3D::Utils::ErrorCode::VulkanShaderModuleCreationFailed,
            QString("Failed to create shader module: %1").arg(result)
        );
    }
    
    return Vizion3D::Utils::Result<VkShaderModule>::success(shaderModule);
}

void VulkanShader::bind(VkCommandBuffer commandBuffer) const {
    if (isValid()) {
        vkCmdBindPipeline(commandBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS, m_pipeline);
    }
}

Vizion3D::Utils::Result<void> VulkanShader::createPipelineLayout(VkDescriptorSetLayout descriptorSetLayout) {
    VkPipelineLayoutCreateInfo pipelineLayoutInfo{};
    pipelineLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;

    if (descriptorSetLayout != VK_NULL_HANDLE) {
        pipelineLayoutInfo.setLayoutCount = 1;
        pipelineLayoutInfo.pSetLayouts = &descriptorSetLayout;
    } else {
        pipelineLayoutInfo.setLayoutCount = 0;
        pipelineLayoutInfo.pSetLayouts = nullptr;
    }

    pipelineLayoutInfo.pushConstantRangeCount = 0;
    pipelineLayoutInfo.pPushConstantRanges = nullptr;
    
    VkResult result = vkCreatePipelineLayout(m_device, &pipelineLayoutInfo, nullptr, &m_pipelineLayout);
    if (result != VK_SUCCESS) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanPipelineCreationFailed,
            QString("Failed to create pipeline layout: %1").arg(result)
        );
    }
    
    return Vizion3D::Utils::Result<void>::success();
}

void VulkanShader::cleanup() {
    if (m_pipeline != VK_NULL_HANDLE && m_device != VK_NULL_HANDLE) {
        vkDestroyPipeline(m_device, m_pipeline, nullptr);
        m_pipeline = VK_NULL_HANDLE;
    }
    
    if (m_pipelineLayout != VK_NULL_HANDLE && m_device != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(m_device, m_pipelineLayout, nullptr);
        m_pipelineLayout = VK_NULL_HANDLE;
    }
    
    if (m_vertexShader != VK_NULL_HANDLE && m_device != VK_NULL_HANDLE) {
        vkDestroyShaderModule(m_device, m_vertexShader, nullptr);
        m_vertexShader = VK_NULL_HANDLE;
    }
    
    if (m_fragmentShader != VK_NULL_HANDLE && m_device != VK_NULL_HANDLE) {
        vkDestroyShaderModule(m_device, m_fragmentShader, nullptr);
        m_fragmentShader = VK_NULL_HANDLE;
    }
    
    m_device = VK_NULL_HANDLE;
    m_context = nullptr;
}

Vizion3D::Utils::Result<void> VulkanShader::createPipeline(const PipelineCreateInfo& createInfo) {
    // Shader stages
    VkPipelineShaderStageCreateInfo vertShaderStageInfo{};
    vertShaderStageInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
    vertShaderStageInfo.stage = VK_SHADER_STAGE_VERTEX_BIT;
    vertShaderStageInfo.module = m_vertexShader;
    vertShaderStageInfo.pName = "main";

    VkPipelineShaderStageCreateInfo fragShaderStageInfo{};
    fragShaderStageInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
    fragShaderStageInfo.stage = VK_SHADER_STAGE_FRAGMENT_BIT;
    fragShaderStageInfo.module = m_fragmentShader;
    fragShaderStageInfo.pName = "main";

    VkPipelineShaderStageCreateInfo shaderStages[] = {vertShaderStageInfo, fragShaderStageInfo};

    // Vertex input
    auto vertexBindings = convertVertexBindings(createInfo.vertexBindings);
    auto vertexAttributes = convertVertexAttributes(createInfo.vertexAttributes);

    VkPipelineVertexInputStateCreateInfo vertexInputInfo{};
    vertexInputInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_VERTEX_INPUT_STATE_CREATE_INFO;
    vertexInputInfo.vertexBindingDescriptionCount = static_cast<uint32_t>(vertexBindings.size());
    vertexInputInfo.pVertexBindingDescriptions = vertexBindings.data();
    vertexInputInfo.vertexAttributeDescriptionCount = static_cast<uint32_t>(vertexAttributes.size());
    vertexInputInfo.pVertexAttributeDescriptions = vertexAttributes.data();

    // Input assembly
    VkPipelineInputAssemblyStateCreateInfo inputAssembly{};
    inputAssembly.sType = VK_STRUCTURE_TYPE_PIPELINE_INPUT_ASSEMBLY_STATE_CREATE_INFO;
    inputAssembly.topology = createInfo.topology;
    inputAssembly.primitiveRestartEnable = VK_FALSE;

    // Viewport and scissor (dynamic)
    VkPipelineViewportStateCreateInfo viewportState{};
    viewportState.sType = VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_STATE_CREATE_INFO;
    viewportState.viewportCount = 1;
    viewportState.scissorCount = 1;

    // Rasterizer
    VkPipelineRasterizationStateCreateInfo rasterizer{};
    rasterizer.sType = VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_STATE_CREATE_INFO;
    rasterizer.depthClampEnable = VK_FALSE;
    rasterizer.rasterizerDiscardEnable = VK_FALSE;
    rasterizer.polygonMode = createInfo.polygonMode;
    rasterizer.lineWidth = 1.0f;
    rasterizer.cullMode = createInfo.cullMode;
    rasterizer.frontFace = createInfo.frontFace;
    rasterizer.depthBiasEnable = VK_FALSE;

    // Multisampling
    VkPipelineMultisampleStateCreateInfo multisampling{};
    multisampling.sType = VK_STRUCTURE_TYPE_PIPELINE_MULTISAMPLE_STATE_CREATE_INFO;
    multisampling.sampleShadingEnable = VK_FALSE;
    multisampling.rasterizationSamples = VK_SAMPLE_COUNT_1_BIT;

    // Depth and stencil testing
    VkPipelineDepthStencilStateCreateInfo depthStencil{};
    depthStencil.sType = VK_STRUCTURE_TYPE_PIPELINE_DEPTH_STENCIL_STATE_CREATE_INFO;
    depthStencil.depthTestEnable = createInfo.depthTestEnable ? VK_TRUE : VK_FALSE;
    depthStencil.depthWriteEnable = createInfo.depthWriteEnable ? VK_TRUE : VK_FALSE;
    depthStencil.depthCompareOp = createInfo.depthCompareOp;
    depthStencil.depthBoundsTestEnable = VK_FALSE;
    depthStencil.stencilTestEnable = VK_FALSE;

    // Color blending
    VkPipelineColorBlendAttachmentState colorBlendAttachment{};
    colorBlendAttachment.colorWriteMask = VK_COLOR_COMPONENT_R_BIT | VK_COLOR_COMPONENT_G_BIT |
                                         VK_COLOR_COMPONENT_B_BIT | VK_COLOR_COMPONENT_A_BIT;
    colorBlendAttachment.blendEnable = createInfo.blendEnable ? VK_TRUE : VK_FALSE;
    if (createInfo.blendEnable) {
        colorBlendAttachment.srcColorBlendFactor = VK_BLEND_FACTOR_SRC_ALPHA;
        colorBlendAttachment.dstColorBlendFactor = VK_BLEND_FACTOR_ONE_MINUS_SRC_ALPHA;
        colorBlendAttachment.colorBlendOp = VK_BLEND_OP_ADD;
        colorBlendAttachment.srcAlphaBlendFactor = VK_BLEND_FACTOR_ONE;
        colorBlendAttachment.dstAlphaBlendFactor = VK_BLEND_FACTOR_ZERO;
        colorBlendAttachment.alphaBlendOp = VK_BLEND_OP_ADD;
    }

    VkPipelineColorBlendStateCreateInfo colorBlending{};
    colorBlending.sType = VK_STRUCTURE_TYPE_PIPELINE_COLOR_BLEND_STATE_CREATE_INFO;
    colorBlending.logicOpEnable = VK_FALSE;
    colorBlending.attachmentCount = 1;
    colorBlending.pAttachments = &colorBlendAttachment;

    // Dynamic state
    std::vector<VkDynamicState> dynamicStates = {
        VK_DYNAMIC_STATE_VIEWPORT,
        VK_DYNAMIC_STATE_SCISSOR
    };

    VkPipelineDynamicStateCreateInfo dynamicState{};
    dynamicState.sType = VK_STRUCTURE_TYPE_PIPELINE_DYNAMIC_STATE_CREATE_INFO;
    dynamicState.dynamicStateCount = static_cast<uint32_t>(dynamicStates.size());
    dynamicState.pDynamicStates = dynamicStates.data();

    // Create graphics pipeline
    VkGraphicsPipelineCreateInfo pipelineInfo{};
    pipelineInfo.sType = VK_STRUCTURE_TYPE_GRAPHICS_PIPELINE_CREATE_INFO;
    pipelineInfo.stageCount = 2;
    pipelineInfo.pStages = shaderStages;
    pipelineInfo.pVertexInputState = &vertexInputInfo;
    pipelineInfo.pInputAssemblyState = &inputAssembly;
    pipelineInfo.pViewportState = &viewportState;
    pipelineInfo.pRasterizationState = &rasterizer;
    pipelineInfo.pMultisampleState = &multisampling;
    pipelineInfo.pDepthStencilState = &depthStencil;
    pipelineInfo.pColorBlendState = &colorBlending;
    pipelineInfo.pDynamicState = &dynamicState;
    pipelineInfo.layout = m_pipelineLayout;
    pipelineInfo.renderPass = createInfo.renderPass;
    pipelineInfo.subpass = createInfo.subpass;
    pipelineInfo.basePipelineHandle = VK_NULL_HANDLE;

    VkResult result = vkCreateGraphicsPipelines(m_device, VK_NULL_HANDLE, 1, &pipelineInfo, nullptr, &m_pipeline);
    if (result != VK_SUCCESS) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanPipelineCreationFailed,
            QString("Failed to create graphics pipeline: %1").arg(result)
        );
    }

    return Vizion3D::Utils::Result<void>::success();
}

std::vector<VkVertexInputBindingDescription> VulkanShader::convertVertexBindings(
    const std::vector<VertexInputBinding>& bindings) const {

    std::vector<VkVertexInputBindingDescription> vkBindings;
    vkBindings.reserve(bindings.size());

    for (const auto& binding : bindings) {
        VkVertexInputBindingDescription vkBinding{};
        vkBinding.binding = binding.binding;
        vkBinding.stride = binding.stride;
        vkBinding.inputRate = binding.inputRate;
        vkBindings.push_back(vkBinding);
    }

    return vkBindings;
}

std::vector<VkVertexInputAttributeDescription> VulkanShader::convertVertexAttributes(
    const std::vector<VertexInputAttribute>& attributes) const {

    std::vector<VkVertexInputAttributeDescription> vkAttributes;
    vkAttributes.reserve(attributes.size());

    for (const auto& attribute : attributes) {
        VkVertexInputAttributeDescription vkAttribute{};
        vkAttribute.location = attribute.location;
        vkAttribute.binding = attribute.binding;
        vkAttribute.format = attribute.format;
        vkAttribute.offset = attribute.offset;
        vkAttributes.push_back(vkAttribute);
    }

    return vkAttributes;
}

// Helper functions
std::pair<std::vector<VulkanShader::VertexInputBinding>,
          std::vector<VulkanShader::VertexInputAttribute>>
createPositionColorVertexInput() {
    std::vector<VulkanShader::VertexInputBinding> bindings = {
        {0, sizeof(float) * 6, VK_VERTEX_INPUT_RATE_VERTEX} // position (3) + color (3)
    };

    std::vector<VulkanShader::VertexInputAttribute> attributes = {
        {0, 0, VK_FORMAT_R32G32B32_SFLOAT, 0},                    // position
        {1, 0, VK_FORMAT_R32G32B32_SFLOAT, sizeof(float) * 3}     // color
    };

    return {bindings, attributes};
}

std::pair<std::vector<VulkanShader::VertexInputBinding>,
          std::vector<VulkanShader::VertexInputAttribute>>
createPositionOnlyVertexInput() {
    std::vector<VulkanShader::VertexInputBinding> bindings = {
        {0, sizeof(float) * 3, VK_VERTEX_INPUT_RATE_VERTEX} // position (3)
    };

    std::vector<VulkanShader::VertexInputAttribute> attributes = {
        {0, 0, VK_FORMAT_R32G32B32_SFLOAT, 0}  // position
    };

    return {bindings, attributes};
}

} // namespace Vizion3D::UI
